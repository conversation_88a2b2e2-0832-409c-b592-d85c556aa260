'use client'

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Star, User, ChevronLeft, ChevronRight } from 'lucide-react';

interface Comment {
  id: string;
  name: string;
  rating: number;
  comment: string;
  avatar?: string;
  date: string;
}

interface CommentSwapperProps {
  comments: Comment[];
  cardWidth?: number;
  cardHeight?: number;
  className?: string;
  autoPlay?: boolean;
  autoPlayInterval?: number;
}

export const CommentSwapper: React.FC<CommentSwapperProps> = ({
  comments,
  cardWidth = 320,  // 20rem = 320px
  cardHeight = 400, // 25rem = 400px
  className = '',
  autoPlay = false,
  autoPlayInterval = 4000
}) => {
  const cardStackRef = useRef<HTMLDivElement>(null);
  const isSwiping = useRef(false);
  const startX = useRef(0);
  const currentX = useRef(0);
  const animationFrameId = useRef<number | null>(null);

  const [currentIndex, setCurrentIndex] = useState(0);
  const [cardOrder, setCardOrder] = useState<number[]>(() =>
    Array.from({ length: comments.length }, (_, i) => i)
  );

  // Navigation functions
  const goToNext = useCallback(() => {
    setCurrentIndex(prev => (prev + 1) % comments.length);
  }, [comments.length]);

  const goToPrevious = useCallback(() => {
    setCurrentIndex(prev => (prev - 1 + comments.length) % comments.length);
  }, [comments.length]);

  const getDurationFromCSS = useCallback((
    variableName: string,
    element?: HTMLElement | null
  ): number => {
    const targetElement = element || document.documentElement;
    const value = getComputedStyle(targetElement)
      ?.getPropertyValue(variableName)
      ?.trim();
    if (!value) return 0;
    if (value.endsWith("ms")) return parseFloat(value);
    if (value.endsWith("s")) return parseFloat(value) * 1000;
    return parseFloat(value) || 0;
  }, []);

  const getCards = useCallback((): HTMLElement[] => {
    if (!cardStackRef.current) return [];
    return [...cardStackRef.current.querySelectorAll('.comment-card')] as HTMLElement[];
  }, []);

  const getActiveCard = useCallback((): HTMLElement | null => {
    const cards = getCards();
    return cards[0] || null;
  }, [getCards]);

  const updatePositions = useCallback(() => {
    const cards = getCards();
    cards.forEach((card, i) => {
      card.style.setProperty('--i', (i + 1).toString());
      card.style.setProperty('--swipe-x', '0px');
      card.style.setProperty('--swipe-rotate', '0deg');
      card.style.opacity = '1';
    });
  }, [getCards]);

  const applySwipeStyles = useCallback((deltaX: number) => {
    const card = getActiveCard();
    if (!card) return;
    card.style.setProperty('--swipe-x', `${deltaX}px`);
    card.style.setProperty('--swipe-rotate', `${deltaX * 0.2}deg`);
    card.style.opacity = (1 - Math.min(Math.abs(deltaX) / 100, 1) * 0.75).toString();
  }, [getActiveCard]);

  const handleStart = useCallback((clientX: number) => {
    if (isSwiping.current) return;
    isSwiping.current = true;
    startX.current = clientX;
    currentX.current = clientX;
    const card = getActiveCard();
    if (card) card.style.transition = 'none';
  }, [getActiveCard]);

  const handleEnd = useCallback(() => {
    if (!isSwiping.current) return;
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }

    const deltaX = currentX.current - startX.current;
    const threshold = 50;
    const duration = getDurationFromCSS('--card-swap-duration', cardStackRef.current);
    const card = getActiveCard();

    if (card) {
      card.style.transition = `transform ${duration}ms ease, opacity ${duration}ms ease`;

      if (Math.abs(deltaX) > threshold) {
        const direction = Math.sign(deltaX);
        card.style.setProperty('--swipe-x', `${direction * 300}px`);
        card.style.setProperty('--swipe-rotate', `${direction * 20}deg`);

        setTimeout(() => {
          if (getActiveCard() === card) {
            card.style.setProperty('--swipe-rotate', `${-direction * 20}deg`);
          }
        }, duration * 0.5);

        setTimeout(() => {
          setCardOrder(prev => {
            if (prev.length === 0) return [];
            return [...prev.slice(1), prev[0]];
          });
        }, duration);
      } else {
        applySwipeStyles(0);
      }
    }

    isSwiping.current = false;
    startX.current = 0;
    currentX.current = 0;
  }, [getDurationFromCSS, getActiveCard, applySwipeStyles]);

  const handleMove = useCallback((clientX: number) => {
    if (!isSwiping.current) return;
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
    }
    animationFrameId.current = requestAnimationFrame(() => {
      currentX.current = clientX;
      const deltaX = currentX.current - startX.current;
      applySwipeStyles(deltaX);

      if (Math.abs(deltaX) > 50) {
        handleEnd();
      }
    });
  }, [applySwipeStyles, handleEnd]);

  useEffect(() => {
    const cardStackElement = cardStackRef.current;
    if (!cardStackElement) return;

    const handlePointerDown = (e: PointerEvent) => {
      handleStart(e.clientX);
    };
    const handlePointerMove = (e: PointerEvent) => {
      handleMove(e.clientX);
    };
    const handlePointerUp = (e: PointerEvent) => {
      handleEnd();
    };

    cardStackElement.addEventListener('pointerdown', handlePointerDown);
    cardStackElement.addEventListener('pointermove', handlePointerMove);
    cardStackElement.addEventListener('pointerup', handlePointerUp);

    return () => {
      cardStackElement.removeEventListener('pointerdown', handlePointerDown);
      cardStackElement.removeEventListener('pointermove', handlePointerMove);
      cardStackElement.removeEventListener('pointerup', handlePointerUp);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [handleStart, handleMove, handleEnd]);

  useEffect(() => {
    updatePositions();
  }, [cardOrder, updatePositions]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        e.preventDefault();
        goToPrevious();
      } else if (e.key === 'ArrowRight') {
        e.preventDefault();
        goToNext();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [goToNext, goToPrevious]);

  // Auto-play functionality
  useEffect(() => {
    if (!autoPlay) return;

    const interval = setInterval(() => {
      goToNext();
    }, autoPlayInterval);

    return () => clearInterval(interval);
  }, [autoPlay, autoPlayInterval, goToNext]);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  // Get visible comments (current and next)
  const getVisibleComments = () => {
    const visible = [];
    for (let i = 0; i < 2; i++) {
      const index = (currentIndex + i) % comments.length;
      visible.push({ comment: comments[index], originalIndex: index, displayIndex: i });
    }
    return visible;
  };

  return (
    <div className={`relative flex items-center gap-8 ${className}`}>
      {/* Previous Arrow */}
      <button
        onClick={goToPrevious}
        className="flex items-center justify-center w-12 h-12 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 text-gray-600 hover:text-gray-800 z-10"
        aria-label="Previous comments"
      >
        <ChevronLeft className="w-6 h-6" />
      </button>

      {/* Cards Container */}
      <section
        className="relative flex gap-6 select-none"
        ref={cardStackRef}
        style={{
          touchAction: 'none',
          '--card-swap-duration': '0.3s',
        } as React.CSSProperties}
      >
        {getVisibleComments().map(({ comment, originalIndex, displayIndex }) => (
          <article
            key={`${comment.id}-${currentIndex}-${displayIndex}`}
            className={`comment-card cursor-grab active:cursor-grabbing
                       bg-stone-50/95 backdrop-blur-sm border border-stone-200 rounded-xl
                       shadow-lg hover:shadow-xl overflow-hidden will-change-transform p-6 transition-all duration-500
                       ${displayIndex === 0 ? 'scale-100' : 'scale-95 opacity-80'}`}
            style={{
              width: cardWidth,
              height: cardHeight,
              transform: `translateX(${displayIndex * 30}px) translateY(${displayIndex * 15}px) rotateY(${displayIndex * -5}deg)`,
              zIndex: 2 - displayIndex,
            } as React.CSSProperties}
          >
            <div className="flex flex-col h-full">
              {/* Header with avatar and name */}
              <div className="flex items-center gap-3 mb-4">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-semibold">
                  {comment.avatar ? (
                    <img 
                      src={comment.avatar} 
                      alt={comment.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-6 h-6" />
                  )}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{comment.name}</h3>
                  <p className="text-sm text-gray-500">{comment.date}</p>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center gap-1 mb-4">
                {renderStars(comment.rating)}
                <span className="ml-2 text-sm text-gray-600">
                  {comment.rating}/5
                </span>
              </div>

              {/* Comment */}
              <div className="flex-1 overflow-y-auto">
                <p className="text-gray-700 leading-relaxed">
                  {comment.comment}
                </p>
              </div>

              {/* Footer with helpful/like actions */}
              <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  Verified Review
                </div>
                <div className="flex items-center gap-2">
                  <button className="text-xs text-blue-600 hover:text-blue-800 transition-colors">
                    Helpful
                  </button>
                  <span className="text-gray-300">•</span>
                  <button className="text-xs text-gray-500 hover:text-gray-700 transition-colors">
                    Share
                  </button>
                </div>
              </div>
            </div>
          </article>
        ))}
      </section>

      {/* Next Arrow */}
      <button
        onClick={goToNext}
        className="flex items-center justify-center w-12 h-12 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 text-gray-600 hover:text-gray-800 z-10"
        aria-label="Next comments"
      >
        <ChevronRight className="w-6 h-6" />
      </button>

      {/* Progress indicator */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 flex items-center gap-2">
        {comments.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentIndex(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 hover:scale-110 ${
              index === currentIndex
                ? 'bg-blue-500 scale-125'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
            aria-label={`Go to comment ${index + 1}`}
          />
        ))}
      </div>
    </div>
  );
};
