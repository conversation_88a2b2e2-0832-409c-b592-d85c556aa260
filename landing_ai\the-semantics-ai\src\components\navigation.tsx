import Link from 'next/link';

export function Navigation() {
  const links = [
    { href: 'https://desktop.thesemantics.com', label: 'Web Development' },
    { href: 'https://mobile.thesemantics.com', label: 'Mobile Apps' },
    { href: 'https://ai.thesemantics.com', label: 'AI Platform' },
    { href: 'https://scraping.thesemantics.com', label: 'Web Scraping' },
    { href: 'https://thesemantics.com', label: 'Main Site' }
  ];

  return (
    <nav aria-label="Main navigation" className="hidden lg:flex space-x-6">
      {links.map((link, index) => (
        <Link 
          key={index}
          href={link.href}
          className="text-white/70 hover:text-white transition-colors"
          rel="noopener"
        >
          {link.label}
        </Link>
      ))}
    </nav>
  );
}