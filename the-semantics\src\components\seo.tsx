import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: object;
}

export function SEO({
  title = "The Semantics - AI & Technology Solutions | LangChain, Machine Learning & Data Science",
  description = "Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development. Transform your business with cutting-edge artificial intelligence.",
  keywords = ["AI development", "artificial intelligence", "LangChain", "RAG", "machine learning", "data science", "semantic AI"],
  ogImage = "/ai-og-image.jpg",
  canonicalUrl,
  structuredData
}: SEOProps) {
  const keywordsString = keywords.join(', ');

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywordsString} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      
      {/* AI service specific meta tags */}
      <meta name="ai-service" content="the-semantics" />
      <meta name="technology-stack" content="ai-ml-data-science" />
      <meta name="ai-capabilities" content="langchain,rag,machine-learning,data-science" />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
    </Head>
  );
}
