'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Users, Award, Clock, Target, Github, Linkedin, Twitter } from 'lucide-react';

export function AboutSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const stats = [
    { icon: Users, number: "113+", label: "Client Reviews" },
    { icon: Award, number: "4.9/5", label: "Fiverr Rating" },
    { icon: Clock, number: "5+", label: "Years Experience" },
    { icon: Target, number: "Level 2", label: "Fiverr Seller" },
  ];

  const team = [
    {
      name: "<PERSON>ar Javed",
      role: "Founder & CEO",
      image: "/umar-javed.jpg",
      bio: "Expert in modern web technologies, AI integration, and scalable application architecture. Specializes in React, Node.js, and machine learning implementations.",
      social: { github: "https://github.com/thesemantics", linkedin: "https://www.linkedin.com/company/thesemanticsco/", twitter: "https://twitter.com/thesemantics" }
    },
    {
      name: "Tahir Siddique",
      role: "Co-Founder & Full Stack Developer",
      image: "/tahir-siddique.jpg",
      bio: "Full stack developer specializing in Python, web scraping, and automation solutions. Expert in creating scalable web applications with a 4.9/5 rating on Fiverr.",
      social: { github: "https://github.com/tahirccreative", linkedin: "https://www.linkedin.com/in/tahirccreative/", twitter: "https://twitter.com/tahirccreative" }
    },
    {
      name: "James Mitchell",
      role: "UI/UX Designer & Frontend Specialist",
      image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face",
      bio: "Creative designer with a passion for crafting intuitive user experiences. Combines aesthetic design with functional frontend development.",
      social: { github: "#", linkedin: "#", twitter: "#" }
    },
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 to-black">
      <div className="container mx-auto px-4">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
              About Us
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              We're a passionate team of mobile developers, designers, and strategists 
              dedicated to creating exceptional mobile experiences that drive business growth.
            </p>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center group"
              >
                <div className="inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-blue-400" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Company Story */}
          <motion.div
            variants={itemVariants}
            className="grid md:grid-cols-2 gap-12 items-center mb-20"
          >
            <div>
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Our Story
              </h3>
              <div className="space-y-4 text-gray-300 leading-relaxed">
                <p>
                  Founded in 2019, we started as a small team of passionate developers 
                  who believed that mobile technology could transform businesses and 
                  improve people's lives.
                </p>
                <p>
                  Today, we've grown into a full-service mobile development agency, 
                  helping startups and enterprises build world-class mobile applications 
                  that users love and businesses depend on.
                </p>
                <p>
                  Our commitment to quality, innovation, and client success has made us 
                  a trusted partner for companies looking to make their mark in the mobile world.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/10 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🚀</div>
                  <div className="text-2xl font-bold text-white mb-2">Innovation First</div>
                  <div className="text-gray-400">Pushing boundaries in mobile development</div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Team */}
          <motion.div variants={containerVariants}>
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Meet Our Team
              </h3>
              <p className="text-gray-300 max-w-2xl mx-auto">
                The talented individuals behind our success, each bringing unique expertise 
                and passion to every project.
              </p>
            </motion.div>

            <div className="grid md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="group text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-white/10 group-hover:border-blue-400/50 transition-colors duration-300">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-white mb-2">{member.name}</h4>
                  <p className="text-blue-400 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-400 text-sm mb-4 leading-relaxed">{member.bio}</p>
                  <div className="flex justify-center space-x-3">
                    <a href={member.social.github} className="text-gray-400 hover:text-white transition-colors">
                      <Github className="w-5 h-5" />
                    </a>
                    <a href={member.social.linkedin} className="text-gray-400 hover:text-white transition-colors">
                      <Linkedin className="w-5 h-5" />
                    </a>
                    <a href={member.social.twitter} className="text-gray-400 hover:text-white transition-colors">
                      <Twitter className="w-5 h-5" />
                    </a>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
