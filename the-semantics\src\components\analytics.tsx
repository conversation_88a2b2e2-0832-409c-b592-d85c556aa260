'use client';

import { useEffect } from 'react';

// Google Analytics component
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  useEffect(() => {
    // Load Google Analytics
    const script1 = document.createElement('script');
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script1.async = true;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        // Enhanced AI service tracking
        send_page_view: true,
        custom_map: {
          'custom_parameter_1': 'ai_service_interest',
          'custom_parameter_2': 'ai_technology_type',
          'custom_parameter_3': 'ai_solution_category'
        }
      });
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [gaId]);

  return null;
}

// Core Web Vitals monitoring for AI services
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        // Log Core Web Vitals with AI service context
        const logVital = (metric: any) => {
          console.log({
            ...metric,
            service: 'ai-technology',
            timestamp: Date.now()
          });
          
          // Send to analytics if available
          if ((window as any).gtag) {
            (window as any).gtag('event', metric.name, {
              event_category: 'Web Vitals - AI',
              event_label: 'ai-technology-service',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        };

        getCLS(logVital);
        getFID(logVital);
        getFCP(logVital);
        getLCP(logVital);
        getTTFB(logVital);
      });
    }
  }, []);

  return null;
}

// AI service-specific event tracking
export function trackAIEvent(action: string, category: string = 'AI Services', label?: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      custom_parameter_1: 'ai_interaction'
    });
  }
}

// Track AI service interest
export function trackAIServiceInterest(serviceType: 'langchain' | 'machine-learning' | 'data-science' | 'semantic-ai' | 'ai-automation') {
  trackAIEvent('ai_service_interest', 'AI Services', serviceType);
}

// Track AI technology inquiries
export function trackAITechnologyInquiry(techType: string) {
  trackAIEvent('ai_technology_inquiry', 'AI Technology', techType);
}

// Track AI solution category engagement
export function trackAISolutionEngagement(solutionCategory: string) {
  trackAIEvent('ai_solution_engagement', 'AI Solutions', solutionCategory);
}

// Track AI consultation requests
export function trackAIConsultationRequest() {
  trackAIEvent('ai_consultation_request', 'AI Consulting', 'consultation');
}

// Track AI project inquiries
export function trackAIProjectInquiry(projectType: string) {
  trackAIEvent('ai_project_inquiry', 'AI Projects', projectType);
}

// Track AI demo interactions
export function trackAIDemoInteraction(demoType: string) {
  trackAIEvent('ai_demo_interaction', 'AI Demos', demoType);
}

// Simple page view tracking
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
}
