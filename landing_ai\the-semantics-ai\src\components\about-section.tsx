'use client'
import React from 'react'
import { Brain, Users, Target, Lightbulb, Award, Globe, Rocket, Heart, Clock, Github, Linkedin, Twitter } from 'lucide-react'
import { cn } from '@/lib/utils'


const values = [
  {
    icon: Brain,
    title: "Genetic Innovation",
    description: "We engineer the DNA of AI technology, creating evolutionary leaps that were previously impossible.",
    gradient: "from-blue-500 to-cyan-500"
  },
  {
    icon: Users,
    title: "Symbiotic Evolution",
    description: "Our AI forms a symbiotic relationship with human intelligence, evolving together for mutual benefit.",
    gradient: "from-purple-500 to-pink-500"
  },
  {
    icon: Target,
    title: "Genetic Precision",
    description: "Every algorithm is genetically engineered for maximum accuracy, like DNA's error-correction mechanisms.",
    gradient: "from-green-500 to-emerald-500"
  },
  {
    icon: Heart,
    title: "Ethical Genome",
    description: "We embed ethical principles into the genetic code of our AI, ensuring responsible evolution.",
    gradient: "from-red-500 to-orange-500"
  }
]

const team = [
  {
    name: "<PERSON><PERSON> Javed",
    role: "Founder & CEO",
    bio: "Expert in modern web technologies, AI integration, and scalable application architecture. Leads the vision for AI-powered semantic solutions.",
    image: "/umar-javed.jpg",
    social: { github: "https://github.com/thesemantics", linkedin: "https://www.linkedin.com/company/thesemanticsco/", twitter: "https://twitter.com/thesemantics" }
  },
  {
    name: "Tahir Siddique",
    role: "Co-Founder & Full Stack Developer",
    bio: "Full stack developer specializing in Python, AI automation, and semantic data processing. Expert in creating intelligent web applications.",
    image: "/tahir-siddique.jpg",
    social: { github: "https://github.com/tahirccreative", linkedin: "https://www.linkedin.com/in/tahirccreative/", twitter: "https://twitter.com/tahirccreative" }
  },
  {
    name: "James Mitchell",
    role: "AI/UX Designer & Frontend Specialist",
    bio: "Creative designer with a passion for crafting intuitive AI experiences. Combines aesthetic design with functional AI-powered frontend development.",
    image: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=300&h=300&fit=crop&crop=face",
    social: { github: "#", linkedin: "#", twitter: "#" }
  },
]

const milestones = [
  { year: "2020", title: "Genesis", description: "The Semantics DNA was conceived from a vision to democratize AI genetics" },
  { year: "2021", title: "First Genetic Code", description: "Launched our proprietary semantic DNA sequencing engine" },
  { year: "2022", title: "Evolutionary Funding", description: "Raised $15M to accelerate AI genetic research and development" },
  { year: "2023", title: "DNA Replication", description: "Pioneered advanced RAG capabilities through genetic programming" },
  { year: "2024", title: "Global Evolution", description: "Our AI DNA now serves 10,000+ organizations across 50 countries" }
]

export function AboutSection() {
  const stats = [
    { icon: Users, number: "10K+", label: "Organizations" },
    { icon: Award, number: "99.9%", label: "AI Uptime" },
    { icon: Clock, number: "4+", label: "Years AI Research" },
    { icon: Target, number: "50+", label: "Countries Served" },
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-b from-gray-200 to-gray-300 relative overflow-hidden">
      {/* DNA Background Elements */}
      <div className="absolute inset-0 opacity-20">
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute top-0 right-0 w-1/3 h-1/2 object-cover opacity-30"
          src="https://ik.imagekit.io/lrigu76hy/tailark/dna-video.mp4?updatedAt=1745736251477">
        </video>
        <video
          autoPlay
          loop
          muted
          playsInline
          className="absolute bottom-0 left-0 w-1/3 h-1/2 object-cover opacity-30 transform rotate-180"
          src="https://ik.imagekit.io/lrigu76hy/tailark/dna-video.mp4?updatedAt=1745736251477">
        </video>

      </div>
      <div className="container mx-auto px-4 relative z-10">
        <div>
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-gray-800 via-blue-600 to-purple-600 bg-clip-text text-transparent">
              About Us
            </h2>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed">
              Like DNA contains the blueprint of life, we're building the genetic code of artificial intelligence -
              making it accessible, ethical, and transformative for businesses worldwide.
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-20">
            {stats.map((stat, index) => (
              <div
                key={index}
                className="text-center group"
              >
                <div className="inline-flex p-4 rounded-xl bg-gradient-to-br from-blue-500/30 to-purple-500/30 backdrop-blur-sm border border-gray-400/30 mb-4 group-hover:scale-110 transition-transform duration-300">
                  <stat.icon className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-800 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>

          {/* Company Story */}
          <div className="grid md:grid-cols-2 gap-12 items-center mb-20">
            <div>
              <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-6">
                Our Story
              </h3>
              <div className="space-y-4 text-gray-700 leading-relaxed">
                <p>
                  Founded in 2020, we started with a vision to democratize AI genetics -
                  believing that artificial intelligence could be engineered like DNA to
                  transform businesses and improve lives.
                </p>
                <p>
                  Today, we've grown into a specialized AI development agency,
                  helping organizations across 50+ countries build intelligent applications
                  that evolve and adapt like living systems.
                </p>
                <p>
                  Our commitment to ethical AI, genetic precision, and symbiotic evolution has made us
                  a trusted partner for companies looking to harness the power of artificial intelligence.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="aspect-square rounded-2xl bg-gradient-to-br from-blue-500/30 to-purple-500/30 backdrop-blur-sm border border-gray-400/30 p-8 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl mb-4">🧬</div>
                  <div className="text-2xl font-bold text-gray-800 mb-2">AI DNA First</div>
                  <div className="text-gray-600">Engineering the genetic code of intelligence</div>
                </div>
              </div>
            </div>
          </div>

          {/* Team */}
          <div>
            <div className="text-center mb-12">
              <h3 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                Meet Our Team
              </h3>
              <p className="text-gray-700 max-w-2xl mx-auto">
                The talented individuals behind our success, each bringing unique expertise
                and passion to every AI project.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div
                  key={index}
                  className="group text-center"
                >
                  <div className="relative mb-6">
                    <div className="w-32 h-32 mx-auto rounded-full overflow-hidden border-4 border-gray-400/30 group-hover:border-blue-500/70 transition-colors duration-300">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                  </div>
                  <h4 className="text-xl font-bold text-gray-800 mb-2">{member.name}</h4>
                  <p className="text-blue-600 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600 text-sm mb-4 leading-relaxed">{member.bio}</p>
                  <div className="flex justify-center space-x-3">
                    <a href={member.social.github} className="text-gray-600 hover:text-gray-800 transition-colors" aria-label={`${member.name}'s GitHub profile`}>
                      <Github className="w-5 h-5" />
                    </a>
                    <a href={member.social.linkedin} className="text-gray-600 hover:text-gray-800 transition-colors" aria-label={`${member.name}'s LinkedIn profile`}>
                      <Linkedin className="w-5 h-5" />
                    </a>
                    <a href={member.social.twitter} className="text-gray-600 hover:text-gray-800 transition-colors" aria-label={`${member.name}'s Twitter profile`}>
                      <Twitter className="w-5 h-5" />
                    </a>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}