'use client';

import { useEffect } from 'react';

// Google Analytics component
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  useEffect(() => {
    // Load Google Analytics
    const script1 = document.createElement('script');
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script1.async = true;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        // Enhanced AI platform tracking
        send_page_view: true,
        custom_map: {
          'custom_parameter_1': 'ai_platform_interest',
          'custom_parameter_2': 'platform_feature_type',
          'custom_parameter_3': 'enterprise_solution_category',
          'custom_parameter_4': 'ai_technology_stack'
        }
      });
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [gaId]);

  return null;
}

// Core Web Vitals monitoring for AI platform
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        // Log Core Web Vitals with AI platform context
        const logVital = (metric: any) => {
          console.log({
            ...metric,
            platform: 'ai-advanced',
            timestamp: Date.now()
          });
          
          // Send to analytics if available
          if ((window as any).gtag) {
            (window as any).gtag('event', metric.name, {
              event_category: 'Web Vitals - AI Platform',
              event_label: 'ai-platform-advanced',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        };

        getCLS(logVital);
        getFID(logVital);
        getFCP(logVital);
        getLCP(logVital);
        getTTFB(logVital);
      });
    }
  }, []);

  return null;
}

// AI platform-specific event tracking
export function trackAIPlatformEvent(action: string, category: string = 'AI Platform', label?: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      custom_parameter_1: 'ai_platform_interaction'
    });
  }
}

// Track AI platform feature interest
export function trackPlatformFeatureInterest(featureType: 'langchain-rag' | 'data-science' | 'semantic-intelligence' | 'ai-automation' | 'neural-networks' | 'knowledge-graphs') {
  trackAIPlatformEvent('platform_feature_interest', 'Platform Features', featureType);
}

// Track enterprise solution inquiries
export function trackEnterpriseSolutionInquiry(solutionType: string) {
  trackAIPlatformEvent('enterprise_solution_inquiry', 'Enterprise Solutions', solutionType);
}

// Track AI technology stack engagement
export function trackAITechnologyEngagement(techStack: string) {
  trackAIPlatformEvent('ai_technology_engagement', 'AI Technology', techStack);
}

// Track platform demo interactions
export function trackPlatformDemoInteraction(demoType: string) {
  trackAIPlatformEvent('platform_demo_interaction', 'Platform Demos', demoType);
}

// Track platform trial requests
export function trackPlatformTrialRequest() {
  trackAIPlatformEvent('platform_trial_request', 'Platform Trial', 'trial-signup');
}

// Track enterprise consultation requests
export function trackEnterpriseConsultationRequest() {
  trackAIPlatformEvent('enterprise_consultation_request', 'Enterprise Consulting', 'consultation');
}

// Track platform documentation views
export function trackDocumentationView(docSection: string) {
  trackAIPlatformEvent('documentation_view', 'Documentation', docSection);
}

// Track platform tutorial engagement
export function trackTutorialEngagement(tutorialType: string) {
  trackAIPlatformEvent('tutorial_engagement', 'Tutorials', tutorialType);
}

// Track case study views
export function trackCaseStudyView(caseStudyType: string) {
  trackAIPlatformEvent('case_study_view', 'Case Studies', caseStudyType);
}

// Simple page view tracking
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
}
