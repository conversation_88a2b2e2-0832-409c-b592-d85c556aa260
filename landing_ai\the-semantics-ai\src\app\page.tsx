import { HeroSection } from '@/components/hero-section'
import { FeaturesSection } from '@/components/features-section'
import { AboutSection } from '@/components/about-section'
import { ReviewsSection } from '@/components/reviews-section'
import { ContactSection } from '@/components/contact-section'
import { Footer } from '@/components/footer'

import { Metadata } from "next";

export const metadata: Metadata = {
  title: "The Semantics - Advanced AI Platform | LangChain RAG, Data Science & Semantic Intelligence",
  description: "Unlock AI's true potential with The Semantics advanced AI platform. Powered by LangChain RAG, cutting-edge data science, and semantic understanding. Transform your data into intelligent insights with enterprise-grade AI solutions.",
  openGraph: {
    title: "The Semantics - Advanced AI Platform",
    description: "Unlock AI's true potential with our advanced AI platform. Powered by LangChain RAG, cutting-edge data science, and semantic understanding.",
    type: "website",
  },
};

export default function Home() {
  return (
    <main className="min-h-screen bg-black" role="main" aria-label="Main content">
      <HeroSection />
      <FeaturesSection />
      <AboutSection />
      <ReviewsSection />
      <ContactSection />
      <Footer />
    </main>
  );
}
