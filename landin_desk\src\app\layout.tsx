import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { WebVitals } from "@/components/web-vitals";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "The Semantics - Web Development & Desktop Apps",
  description: "Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.",
  keywords: ["web development", "desktop applications", "full-stack development", "UI/UX design", "enterprise solutions", "modern web apps", "custom software"],
  authors: [{ name: "The Semantics Team" }],
  creator: "The Semantics",
  publisher: "The Semantics",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://desktop.thesemantics.com",
    title: "The Semantics - Web Development & Desktop Apps",
    description: "Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.",
    siteName: "The Semantics",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Semantics - Web Development & Desktop Apps",
    description: "Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design.",
    creator: "@thesemantics",
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://desktop.thesemantics.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Semantics",
    "url": "https://desktop.thesemantics.com",
    "logo": "https://thesemantics.com/logo.png",
    "description": "Professional web development and desktop application solutions",
    "foundingDate": "2020",
    "sameAs": [
      "https://linkedin.com/company/thesemantics",
      "https://github.com/thesemantics",
      "https://twitter.com/thesemantics"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "service": [
      {
        "@type": "Service",
        "name": "Web Development",
        "description": "Custom web application development using modern technologies"
      },
      {
        "@type": "Service",
        "name": "Desktop Applications",
        "description": "Cross-platform desktop application development"
      }
    ]
  };

  return (
    <html lang="en">
      <head>
        {/* Explicit meta tags for better SEO */}
        <meta name="description" content="Transform your business with cutting-edge web development and desktop applications. Expert full-stack development and modern UI/UX design." />
        <meta name="keywords" content="web development, desktop applications, full-stack development, UI/UX design, enterprise solutions, modern web apps, custom software" />
        <meta name="author" content="The Semantics Team" />
        <meta name="robots" content="index, follow" />

        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://images.unsplash.com" />
        <link rel="dns-prefetch" href="https://www.linkedin.com" />
        <link rel="dns-prefetch" href="https://www.upwork.com" />
        <link rel="dns-prefetch" href="https://www.fiverr.com" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebVitals />
        {children}
      </body>
    </html>
  );
}
