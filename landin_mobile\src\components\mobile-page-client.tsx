'use client';

import React, { useEffect, useState } from 'react';
import { Navigation } from "@/components/blocks/navigation";
import { HeroSection } from "@/components/blocks/demo";
import { ServicesSection } from "@/components/blocks/services-section";
import { AboutSection } from "@/components/blocks/about-section";
import { TestimonialsSection } from "@/components/blocks/testimonials-section";
import { Footer } from "@/components/blocks/footer";
import { LoadingScreen } from "@/components/ui/loading";

export function MobilePageClient() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Add smooth scrolling to the entire page
    document.documentElement.style.scrollBehavior = 'smooth';

    // Simulate loading time for better UX
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return () => {
      document.documentElement.style.scrollBehavior = 'auto';
      clearTimeout(timer);
    };
  }, []);

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <>
      <Navigation />

      <main>
        <section id="hero" aria-label="Hero section">
          <HeroSection />
        </section>

        <section id="services" aria-label="Mobile development services">
          <ServicesSection />
        </section>

        <section id="about" aria-label="About The Semantics">
          <AboutSection />
        </section>

        <section id="testimonials" aria-label="Client testimonials">
          <TestimonialsSection />
        </section>
      </main>

      <footer id="contact" aria-label="Contact information">
        <Footer />
      </footer>
    </>
  );
}
