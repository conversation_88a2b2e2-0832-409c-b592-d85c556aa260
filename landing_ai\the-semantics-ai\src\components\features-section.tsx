'use client'
import React from 'react'
import { Brain, Database, Zap, Bot, Search, BarChart3, Network, Cpu, MessageSquare, FileText, TrendingUp, Shield } from 'lucide-react'
import { cn } from '@/lib/utils'
import { motion } from 'framer-motion'

const features = [
  {
    icon: Brain,
    title: "LangChain RAG",
    description: "Like DNA replication, our RAG system accurately retrieves and generates contextual responses from your data genome.",
    gradient: "from-blue-500 to-cyan-500",
    bgGradient: "from-blue-50 to-cyan-50 dark:from-blue-950 dark:to-cyan-950"
  },
  {
    icon: Database,
    title: "Data Science Platform",
    description: "Comprehensive data analysis that sequences your business genome, revealing patterns hidden in your organizational DNA.",
    gradient: "from-purple-500 to-pink-500",
    bgGradient: "from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950"
  },
  {
    icon: Search,
    title: "Semantic Search",
    description: "Intelligent search that decodes the genetic markers of meaning, understanding context like DNA base pairs.",
    gradient: "from-green-500 to-emerald-500",
    bgGradient: "from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950"
  },
  {
    icon: <PERSON><PERSON>,
    title: "AI Automation",
    description: "Self-replicating automation that evolves and adapts, carrying forward the genetic code of efficiency.",
    gradient: "from-orange-500 to-red-500",
    bgGradient: "from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950"
  },
  {
    icon: BarChart3,
    title: "Analytics & Insights",
    description: "Genetic sequencing for your data, revealing hereditary patterns and predicting evolutionary trends.",
    gradient: "from-indigo-500 to-blue-500",
    bgGradient: "from-indigo-50 to-blue-50 dark:from-indigo-950 dark:to-blue-950"
  },
  {
    icon: Network,
    title: "Knowledge Graphs",
    description: "Interconnected knowledge networks that mirror the complex relationships found in biological systems.",
    gradient: "from-teal-500 to-cyan-500",
    bgGradient: "from-teal-50 to-cyan-50 dark:from-teal-950 dark:to-cyan-950"
  }
]

const stats = [
  { value: "99.9%", label: "Genetic Accuracy", icon: TrendingUp },
  { value: "10x", label: "DNA Replication Speed", icon: Zap },
  { value: "500+", label: "Data Genomes", icon: Database },
  { value: "24/7", label: "Evolutionary Monitoring", icon: Shield }
]

export function FeaturesSection() {
  return (
    <section id="features" className="py-24 bg-gradient-to-b from-background to-muted/20 relative overflow-hidden" aria-label="AI Platform Features">
      {/* DNA Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 border-2 border-blue-500 rounded-full animate-pulse"></div>
        <div className="absolute top-32 right-20 w-24 h-24 border-2 border-purple-500 rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-20 left-1/4 w-40 h-40 border-2 border-cyan-500 rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-32 right-1/3 w-28 h-28 border-2 border-indigo-500 rounded-full animate-pulse delay-3000"></div>
        {/* DNA Helix Pattern */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1200 800" fill="none">
          <path d="M100 100 Q 300 200 500 100 T 900 100" stroke="url(#dna-gradient)" strokeWidth="2" opacity="0.3" />
          <path d="M100 200 Q 300 100 500 200 T 900 200" stroke="url(#dna-gradient)" strokeWidth="2" opacity="0.3" />
          <path d="M100 300 Q 300 400 500 300 T 900 300" stroke="url(#dna-gradient)" strokeWidth="2" opacity="0.3" />
          <path d="M100 400 Q 300 300 500 400 T 900 400" stroke="url(#dna-gradient)" strokeWidth="2" opacity="0.3" />
          <defs>
            <linearGradient id="dna-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3B82F6" />
              <stop offset="50%" stopColor="#8B5CF6" />
              <stop offset="100%" stopColor="#06B6D4" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="mx-auto max-w-7xl px-6 lg:px-12 relative z-10">
        {/* Header */}
        <header className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white drop-shadow-2xl">
              DNA of <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Intelligence</span>
            </h2>
            <p className="text-xl text-gray-200 max-w-3xl mx-auto drop-shadow-xl">
              Like DNA carries genetic information, our advanced AI platform carries the building blocks of intelligent automation, LangChain RAG, and semantic understanding for enterprise solutions.
            </p>
          </motion.div>
        </header>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className={cn(
                "relative p-8 rounded-2xl border bg-gradient-to-br",
                feature.bgGradient,
                "hover:shadow-lg transition-all duration-300 group hover:scale-105"
              )}
            >
              <div className={cn(
                "inline-flex p-3 rounded-xl bg-gradient-to-r mb-4",
                feature.gradient
              )}>
                <feature.icon className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                {feature.title}
              </h3>
              <p className="text-muted-foreground leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 md:p-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Genetic Performance Markers
            </h3>
            <p className="text-blue-100 text-lg">
              Real results from our DNA-powered AI evolution
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex p-3 rounded-xl bg-white/10 mb-3">
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.value}
                </div>
                <div className="text-blue-100 text-sm md:text-base">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}
