'use client';

import { useEffect } from 'react';

// Google Analytics component
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  useEffect(() => {
    // Load Google Analytics
    const script1 = document.createElement('script');
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script1.async = true;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        // Enhanced mobile tracking
        send_page_view: true,
        custom_map: {
          'custom_parameter_1': 'mobile_app_interest'
        }
      });
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [gaId]);

  return null;
}

// Core Web Vitals monitoring with mobile focus
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        // Log Core Web Vitals with mobile context
        const logVital = (metric: any) => {
          const isMobile = window.innerWidth <= 768;
          console.log({
            ...metric,
            device: isMobile ? 'mobile' : 'desktop',
            timestamp: Date.now()
          });
          
          // Send to analytics if available
          if ((window as any).gtag) {
            (window as any).gtag('event', metric.name, {
              event_category: 'Web Vitals',
              event_label: isMobile ? 'mobile' : 'desktop',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        };

        getCLS(logVital);
        getFID(logVital);
        getFCP(logVital);
        getLCP(logVital);
        getTTFB(logVital);
      });
    }
  }, []);

  return null;
}

// Mobile-specific event tracking
export function trackMobileEvent(action: string, category: string = 'Mobile', label?: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      custom_parameter_1: 'mobile_interaction'
    });
  }
}

// Track mobile app interest
export function trackAppInterest(appType: 'react-native' | 'flutter' | 'native-ios' | 'native-android') {
  trackMobileEvent('app_interest', 'Mobile Development', appType);
}

// Simple page view tracking
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
}
