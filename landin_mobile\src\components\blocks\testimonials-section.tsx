'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Star, ChevronLeft, ChevronRight, Quote } from 'lucide-react';

export function TestimonialsSection() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  const [currentIndex, setCurrentIndex] = useState(0);

  // Generate unique mobile-themed placeholder avatar
  const getMobilePlaceholder = (name: string, index: number) => {
    const colors = ['#06B6D4', '#8B5CF6', '#10B981', '#F59E0B', '#EF4444'];
    const color = colors[index % colors.length];
    const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="mobileGrad${index}" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${color}70;stop-opacity:1" />
          </linearGradient>
        </defs>
        <rect width="150" height="150" fill="url(#mobileGrad${index})" rx="30"/>
        <rect x="25" y="20" width="100" height="60" fill="white" fill-opacity="0.2" rx="15"/>
        <rect x="30" y="25" width="90" height="50" fill="white" fill-opacity="0.1" rx="12"/>
        <text x="75" y="110" font-family="Arial, sans-serif" font-size="28" font-weight="bold" text-anchor="middle" fill="white">${initials}</text>
        <circle cx="75" cy="35" r="3" fill="white" fill-opacity="0.8"/>
        <rect x="40" y="45" width="70" height="3" fill="white" fill-opacity="0.6" rx="1.5"/>
        <rect x="40" y="52" width="50" height="3" fill="white" fill-opacity="0.4" rx="1.5"/>
      </svg>
    `)}`;
  };

  const testimonials = [
    {
      name: "Aaron Starrett",
      role: "Business Owner",
      company: "United States",
      image: getMobilePlaceholder("Aaron Starrett", 0),
      rating: 5,
      text: "Tahir Siddique exceeded expectations with his professionalism and well-documented software solutions. Working with him was seamless due to his proactive communication and fluency, not to mention he always delivered on time. Always great to work with, will help you before during and after delivery.",
      project: "Mobile App Development"
    },
    {
      name: "King Co",
      role: "Project Manager",
      company: "United States",
      image: getMobilePlaceholder("King Co", 1),
      rating: 5,
      text: "Wow, Tahir is amazing. I have tried to work with a few developers on Fiverr and none of them ever proactively went deep enough with me to truly understand the scope of the project. Tahir exceeded my expectations and will be working with him in the future!!",
      project: "Cross-Platform Mobile Solution"
    },
    {
      name: "Yunik Design",
      role: "Design Director",
      company: "France",
      image: getMobilePlaceholder("Yunik Design", 2),
      rating: 5,
      text: "I had the pleasure of working with Tahir, and I must say, he truly exceeded all my expectations. His documentation was clear and comprehensive, reflecting his deep understanding and expertise in code. Every detail was meticulously attended to, ensuring the final product was completely bug-free.",
      project: "Mobile UI/UX Implementation"
    },
    {
      name: "Thibault Armand",
      role: "Data Analyst",
      company: "United States",
      image: getMobilePlaceholder("Thibault Armand", 3),
      rating: 5,
      text: "I had the pleasure to hire Tahir for a mobile data solution. His understanding of the problem, ability to propose solutions and deliver them were outstanding. Very professional and knowledgeable! He understood my project perfectly and created exactly what I needed.",
      project: "Mobile Data Analytics App"
    },
    {
      name: "Sarah Mitchell",
      role: "Startup Founder",
      company: "Canada",
      image: getMobilePlaceholder("Sarah Mitchell", 4),
      rating: 5,
      text: "The Semantics team delivered an exceptional mobile app that transformed our business operations. Their expertise in both iOS and Android development, combined with their attention to user experience, resulted in a 5-star app that our customers love. Highly recommended!",
      project: "Business Management App"
    }
  ];

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  // Auto-advance testimonials
  useEffect(() => {
    const interval = setInterval(nextTestimonial, 5000);
    return () => clearInterval(interval);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        duration: 0.6,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      zIndex: 1,
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      zIndex: 0,
      x: direction < 0 ? 1000 : -1000,
      opacity: 0
    })
  };

  return (
    <section className="py-20 bg-gradient-to-b from-black to-gray-900">
      <div className="container mx-auto px-4">
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
              Client Reviews
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Don't just take our word for it. Here's what our clients say about 
              working with us and the results we've delivered.
            </p>
          </motion.div>

          {/* Testimonial Carousel */}
          <motion.div variants={itemVariants} className="relative max-w-4xl mx-auto">
            <div className="relative h-96 overflow-hidden">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentIndex}
                  custom={1}
                  variants={slideVariants}
                  initial="enter"
                  animate="center"
                  exit="exit"
                  transition={{
                    x: { type: "spring", stiffness: 300, damping: 30 },
                    opacity: { duration: 0.2 }
                  }}
                  className="absolute inset-0"
                >
                  <div className="bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm border border-white/10 rounded-2xl p-8 h-full flex flex-col justify-between">
                    {/* Quote Icon */}
                    <div className="flex justify-center mb-6">
                      <Quote className="w-12 h-12 text-blue-400 opacity-50" />
                    </div>

                    {/* Testimonial Text */}
                    <div className="flex-1 flex items-center">
                      <p className="text-lg md:text-xl text-gray-200 leading-relaxed text-center">
                        "{testimonials[currentIndex].text}"
                      </p>
                    </div>

                    {/* Client Info */}
                    <div className="flex items-center justify-center mt-8">
                      <img
                        src={testimonials[currentIndex].image}
                        alt={testimonials[currentIndex].name}
                        className="w-16 h-16 rounded-full border-2 border-white/20 mr-4"
                      />
                      <div className="text-left">
                        <div className="font-bold text-white text-lg">
                          {testimonials[currentIndex].name}
                        </div>
                        <div className="text-blue-400 text-sm">
                          {testimonials[currentIndex].role}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {testimonials[currentIndex].company}
                        </div>
                        {/* Rating */}
                        <div className="flex mt-2">
                          {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                            <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>

            {/* Navigation Buttons */}
            <button
              onClick={prevTestimonial}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-full p-3 transition-all duration-300 group"
            >
              <ChevronLeft className="w-6 h-6 text-white group-hover:scale-110 transition-transform" />
            </button>
            <button
              onClick={nextTestimonial}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/10 hover:bg-white/20 backdrop-blur-sm border border-white/20 rounded-full p-3 transition-all duration-300 group"
            >
              <ChevronRight className="w-6 h-6 text-white group-hover:scale-110 transition-transform" />
            </button>

            {/* Dots Indicator */}
            <div className="flex justify-center mt-8 space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentIndex
                      ? 'bg-blue-400 scale-125'
                      : 'bg-white/30 hover:bg-white/50'
                  }`}
                />
              ))}
            </div>
          </motion.div>

          {/* Stats */}
          <motion.div
            variants={containerVariants}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16"
          >
            {[
              { number: "4.9/5", label: "Fiverr Rating" },
              { number: "113+", label: "Client Reviews" },
              { number: "5+", label: "Years Experience" },
              { number: "Level 2", label: "Fiverr Seller" },
            ].map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-white mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
