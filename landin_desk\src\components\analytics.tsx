'use client';

import { useEffect } from 'react';

// Google Analytics component
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  useEffect(() => {
    // Load Google Analytics
    const script1 = document.createElement('script');
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script1.async = true;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}');
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [gaId]);

  return null;
}

// Core Web Vitals monitoring
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Simple web vitals tracking without dynamic import
      console.log('Web Vitals tracking initialized for desktop platform');
    }
  }, []);

  return null;
}

// Simple page view tracking
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
}
