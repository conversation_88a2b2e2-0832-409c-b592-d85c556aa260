'use client'

import { motion } from 'framer-motion'
import { Star } from 'lucide-react'

const reviews = [
  {
    name: "TechCorp AI",
    role: "Chief Data Officer",
    company: "Fortune 500 Company",
    rating: 5,
    text: "The Semantics AI platform revolutionized our data processing capabilities. The semantic understanding and LangChain integration delivered insights we never thought possible.",
    project: "Enterprise AI Implementation"
  },
  {
    name: "InnovateLabs",
    role: "AI Research Director",
    company: "Research Institute",
    rating: 5,
    text: "Exceptional AI platform with cutting-edge semantic intelligence. The team's expertise in LangChain RAG and data science is unmatched in the industry.",
    project: "Advanced RAG System"
  },
  {
    name: "DataDriven Solutions",
    role: "Machine Learning Lead",
    company: "AI Startup",
    rating: 5,
    text: "Outstanding platform that seamlessly integrates with our existing infrastructure. The semantic DNA approach to AI is truly innovative and powerful.",
    project: "Semantic AI Integration"
  },
  {
    name: "FutureTech Enterprises",
    role: "VP of Technology",
    company: "Technology Company",
    rating: 5,
    text: "The Semantics platform exceeded our expectations. Professional implementation, robust architecture, and incredible results for our AI initiatives.",
    project: "AI Platform Migration"
  }
]

// Generate unique AI platform-themed placeholder
const getAIPlatformPlaceholder = (name: string, index: number) => {
  const colors = ['#6366F1', '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B'];
  const color = colors[index % colors.length];
  const initials = name.split(' ').map(n => n[0]).join('').toUpperCase();
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="150" height="150" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="aiPlatformGrad${index}" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
          <stop offset="100%" style="stop-color:${color}40;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="150" height="150" fill="url(#aiPlatformGrad${index})" rx="30"/>
      <circle cx="75" cy="50" r="20" fill="white" fill-opacity="0.2"/>
      <circle cx="75" cy="50" r="12" fill="white" fill-opacity="0.4"/>
      <circle cx="75" cy="50" r="6" fill="white" fill-opacity="0.8"/>
      <text x="75" y="110" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${initials}</text>
      <rect x="25" y="80" width="100" height="2" fill="white" fill-opacity="0.5" rx="1"/>
      <rect x="35" y="90" width="80" height="2" fill="white" fill-opacity="0.3" rx="1"/>
    </svg>
  `)}`;
};

export function ReviewsSection() {
  return (
    <section className="py-20 px-8" aria-label="Client reviews">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Trusted by <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">AI Leaders</span>
          </h2>
          <p className="text-muted-foreground text-lg max-w-3xl mx-auto">
            See how organizations worldwide are leveraging our AI platform to unlock semantic intelligence
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
          {reviews.map((review, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-8 hover:border-primary/50 transition-all duration-300"
            >
              <div className="flex items-center mb-6">
                <img
                  src={getAIPlatformPlaceholder(review.name, index)}
                  alt={review.name}
                  className="w-16 h-16 rounded-full mr-4"
                />
                <div>
                  <h4 className="font-semibold text-lg">{review.name}</h4>
                  <p className="text-muted-foreground text-sm">{review.role}</p>
                  <p className="text-muted-foreground text-xs">{review.company}</p>
                </div>
              </div>
              
              <div className="flex mb-4">
                {[...Array(review.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              
              <p className="text-muted-foreground leading-relaxed mb-4">{review.text}</p>
              
              <div className="text-sm text-primary font-medium">
                Project: {review.project}
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
