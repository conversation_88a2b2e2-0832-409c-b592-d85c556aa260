import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: object;
}

export function SEO({
  title = "The Semantics - Professional Web Scraping Services | Python Data Extraction",
  description = "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical, and scalable data collection solutions for businesses worldwide.",
  keywords = ["web scraping", "data extraction", "Python scraping", "BeautifulSoup", "Scrapy", "data mining", "web crawling"],
  ogImage = "/scraping-og-image.jpg",
  canonicalUrl,
  structuredData
}: SEOProps) {
  const keywordsString = keywords.join(', ');

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywordsString} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      
      {/* Web scraping specific meta tags */}
      <meta name="robots" content="index, follow" />
      <meta name="scraping-policy" content="ethical-only" />
      <meta name="data-extraction" content="professional-services" />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
    </Head>
  );
}
