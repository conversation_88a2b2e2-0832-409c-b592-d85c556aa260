#!/usr/bin/env node

/**
 * Free SEO Analyzer for The Semantics Landing Pages
 * Analyzes unpublished Next.js landing pages for SEO score
 */

const fs = require('fs');
const path = require('path');

class SEOAnalyzer {
  constructor() {
    this.results = {};
    this.landingPages = [
      { name: 'Desktop Landing', path: 'landin_desk' },
      { name: 'Mobile Landing', path: 'landin_mobile' },
      { name: 'AI Platform', path: 'landing_ai/the-semantics-ai' },
      { name: 'Web Scraping', path: 'landing_scraping' },
      { name: 'The Semantics', path: 'the-semantics' }
    ];
  }

  analyzeMetadata(layoutContent) {
    const score = { total: 0, max: 100, details: [] };
    
    // Check for title
    if (layoutContent.includes('title:')) {
      const titleMatch = layoutContent.match(/title:\s*["']([^"']+)["']/);
      if (titleMatch) {
        const title = titleMatch[1];
        if (title.length >= 30 && title.length <= 60) {
          score.total += 15;
          score.details.push('✅ Title length optimal (30-60 chars)');
        } else {
          score.details.push(`⚠️ Title length: ${title.length} chars (optimal: 30-60)`);
        }
      }
    } else {
      score.details.push('❌ No title found');
    }

    // Check for description
    if (layoutContent.includes('description:')) {
      const descMatch = layoutContent.match(/description:\s*["']([^"']+)["']/);
      if (descMatch) {
        const desc = descMatch[1];
        if (desc.length >= 120 && desc.length <= 160) {
          score.total += 15;
          score.details.push('✅ Description length optimal (120-160 chars)');
        } else {
          score.details.push(`⚠️ Description length: ${desc.length} chars (optimal: 120-160)`);
        }
      }
    } else {
      score.details.push('❌ No meta description found');
    }

    // Check for keywords
    if (layoutContent.includes('keywords:')) {
      score.total += 10;
      score.details.push('✅ Keywords meta tag present');
    } else {
      score.details.push('❌ No keywords meta tag');
    }

    // Check for Open Graph
    if (layoutContent.includes('openGraph:')) {
      score.total += 15;
      score.details.push('✅ Open Graph tags present');
    } else {
      score.details.push('❌ No Open Graph tags');
    }

    // Check for Twitter Cards
    if (layoutContent.includes('twitter:')) {
      score.total += 10;
      score.details.push('✅ Twitter Card tags present');
    } else {
      score.details.push('❌ No Twitter Card tags');
    }

    // Check for structured data
    if (layoutContent.includes('structuredData') || layoutContent.includes('@type')) {
      score.total += 20;
      score.details.push('✅ Structured data (JSON-LD) present');
    } else {
      score.details.push('❌ No structured data found');
    }

    // Check for canonical URL
    if (layoutContent.includes('canonical:')) {
      score.total += 5;
      score.details.push('✅ Canonical URL specified');
    } else {
      score.details.push('❌ No canonical URL');
    }

    // Check for robots meta
    if (layoutContent.includes('robots:')) {
      score.total += 10;
      score.details.push('✅ Robots meta tags configured');
    } else {
      score.details.push('❌ No robots configuration');
    }

    return score;
  }

  analyzeTechnicalSEO(projectPath) {
    const score = { total: 0, max: 50, details: [] };

    // Check for robots.txt
    const robotsPath = path.join(projectPath, 'public', 'robots.txt');
    if (fs.existsSync(robotsPath)) {
      score.total += 10;
      score.details.push('✅ robots.txt file present');
    } else {
      score.details.push('❌ No robots.txt file');
    }

    // Check for sitemap
    const sitemapPath = path.join(projectPath, 'src', 'app', 'sitemap.ts');
    if (fs.existsSync(sitemapPath)) {
      score.total += 15;
      score.details.push('✅ Sitemap configuration present');
    } else {
      score.details.push('❌ No sitemap configuration');
    }

    // Check for favicon
    const faviconPath = path.join(projectPath, 'src', 'app', 'favicon.ico');
    if (fs.existsSync(faviconPath)) {
      score.total += 5;
      score.details.push('✅ Favicon present');
    } else {
      score.details.push('❌ No favicon found');
    }

    // Check for next.config optimization
    const nextConfigPath = path.join(projectPath, 'next.config.ts');
    if (fs.existsSync(nextConfigPath)) {
      const configContent = fs.readFileSync(nextConfigPath, 'utf8');
      if (configContent.includes('compress') || configContent.includes('optimization')) {
        score.total += 10;
        score.details.push('✅ Next.js optimization configured');
      } else {
        score.details.push('⚠️ Basic Next.js config (could be optimized)');
      }
    } else {
      score.details.push('❌ No Next.js config file');
    }

    // Check for analytics
    const analyticsPath = path.join(projectPath, 'src', 'components', 'analytics.tsx');
    if (fs.existsSync(analyticsPath)) {
      score.total += 10;
      score.details.push('✅ Analytics component present');
    } else {
      score.details.push('❌ No analytics component');
    }

    return score;
  }

  analyzeContent(projectPath) {
    const score = { total: 0, max: 30, details: [] };

    // Check main page content
    const pagePath = path.join(projectPath, 'src', 'app', 'page.tsx');
    if (fs.existsSync(pagePath)) {
      const pageContent = fs.readFileSync(pagePath, 'utf8');
      
      // Check for semantic HTML
      if (pageContent.includes('<h1') || pageContent.includes('h1')) {
        score.total += 10;
        score.details.push('✅ H1 heading structure present');
      } else {
        score.details.push('❌ No H1 heading found');
      }

      // Check for alt attributes (in components)
      if (pageContent.includes('alt=') || pageContent.includes('alt:')) {
        score.total += 5;
        score.details.push('✅ Alt attributes present');
      } else {
        score.details.push('⚠️ Alt attributes may be missing');
      }

      // Check for semantic HTML tags
      const semanticTags = ['<section', '<article', '<nav', '<header', '<footer', '<main'];
      const foundTags = semanticTags.filter(tag => pageContent.includes(tag));
      if (foundTags.length >= 3) {
        score.total += 10;
        score.details.push('✅ Good semantic HTML structure');
      } else {
        score.details.push(`⚠️ Limited semantic HTML (found: ${foundTags.length}/6 tags)`);
      }

      // Check for internal linking structure
      if (pageContent.includes('href=') || pageContent.includes('Link')) {
        score.total += 5;
        score.details.push('✅ Internal linking present');
      } else {
        score.details.push('⚠️ Limited internal linking');
      }
    } else {
      score.details.push('❌ Main page file not found');
    }

    return score;
  }

  async analyzePage(landingPage) {
    console.log(`\n🔍 Analyzing: ${landingPage.name}`);
    console.log('=' .repeat(50));

    const projectPath = path.join(process.cwd(), landingPage.path);
    const layoutPath = path.join(projectPath, 'src', 'app', 'layout.tsx');

    if (!fs.existsSync(projectPath)) {
      return {
        name: landingPage.name,
        error: 'Project directory not found',
        totalScore: 0
      };
    }

    let layoutContent = '';
    if (fs.existsSync(layoutPath)) {
      layoutContent = fs.readFileSync(layoutPath, 'utf8');
    }

    // Analyze different aspects
    const metadataScore = this.analyzeMetadata(layoutContent);
    const technicalScore = this.analyzeTechnicalSEO(projectPath);
    const contentScore = this.analyzeContent(projectPath);

    const totalScore = Math.round(
      ((metadataScore.total / metadataScore.max) * 60) +
      ((technicalScore.total / technicalScore.max) * 25) +
      ((contentScore.total / contentScore.max) * 15)
    );

    const result = {
      name: landingPage.name,
      path: landingPage.path,
      totalScore,
      grade: this.getGrade(totalScore),
      metadata: metadataScore,
      technical: technicalScore,
      content: contentScore,
      recommendations: this.getRecommendations(metadataScore, technicalScore, contentScore)
    };

    this.displayResults(result);
    return result;
  }

  getGrade(score) {
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B';
    if (score >= 60) return 'C';
    if (score >= 50) return 'D';
    return 'F';
  }

  getRecommendations(metadata, technical, content) {
    const recommendations = [];

    // Metadata recommendations
    if (metadata.total < metadata.max * 0.8) {
      recommendations.push('🎯 Optimize metadata: Ensure title and description are within optimal length');
    }

    // Technical recommendations
    if (technical.total < technical.max * 0.8) {
      recommendations.push('⚙️ Improve technical SEO: Add missing robots.txt, sitemap, or optimize Next.js config');
    }

    // Content recommendations
    if (content.total < content.max * 0.8) {
      recommendations.push('📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy');
    }

    return recommendations;
  }

  displayResults(result) {
    console.log(`📊 SEO Score: ${result.totalScore}/100 (Grade: ${result.grade})`);
    console.log('\n📋 Detailed Analysis:');
    
    console.log('\n🏷️  Metadata Analysis:');
    result.metadata.details.forEach(detail => console.log(`   ${detail}`));
    
    console.log('\n⚙️  Technical SEO:');
    result.technical.details.forEach(detail => console.log(`   ${detail}`));
    
    console.log('\n📝 Content Analysis:');
    result.content.details.forEach(detail => console.log(`   ${detail}`));

    if (result.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      result.recommendations.forEach(rec => console.log(`   ${rec}`));
    }
  }

  async analyzeAll() {
    console.log('🚀 The Semantics SEO Analyzer');
    console.log('Analyzing all landing pages for SEO optimization...\n');

    const results = [];
    for (const page of this.landingPages) {
      const result = await this.analyzePage(page);
      results.push(result);
    }

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📈 SEO ANALYSIS SUMMARY');
    console.log('='.repeat(60));

    results.forEach(result => {
      if (!result.error) {
        console.log(`${result.name}: ${result.totalScore}/100 (${result.grade})`);
      } else {
        console.log(`${result.name}: ERROR - ${result.error}`);
      }
    });

    const avgScore = results
      .filter(r => !r.error)
      .reduce((sum, r) => sum + r.totalScore, 0) / 
      results.filter(r => !r.error).length;

    console.log(`\n🎯 Average SEO Score: ${Math.round(avgScore)}/100`);

    // Save detailed report
    const reportPath = 'seo-analysis-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);

    return results;
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new SEOAnalyzer();
  analyzer.analyzeAll().catch(console.error);
}

module.exports = SEOAnalyzer;
