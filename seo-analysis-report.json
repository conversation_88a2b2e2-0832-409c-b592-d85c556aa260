[{"name": "Desktop Landing", "path": "landin_desk", "totalScore": 85, "grade": "A", "metadata": {"total": 100, "max": 100, "details": ["✅ Title length optimal (30-60 chars)", "✅ Description length optimal (120-160 chars)", "✅ Keywords meta tag present", "✅ Open Graph tags present", "✅ Twitter Card tags present", "✅ Structured data (JSON-LD) present", "✅ Canonical URL specified", "✅ Robots meta tags configured"]}, "technical": {"total": 50, "max": 50, "details": ["✅ robots.txt file present", "✅ Sitemap configuration present", "✅ Favicon present", "✅ Next.js optimization configured", "✅ Analytics component present"]}, "content": {"total": 0, "max": 30, "details": ["❌ No H1 heading found", "⚠️ Alt attributes may be missing", "⚠️ Limited semantic HTML (found: 1/6 tags)", "⚠️ Limited internal linking"]}, "recommendations": ["📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy"]}, {"name": "Mobile Landing", "path": "landin_mobile", "totalScore": 85, "grade": "A", "metadata": {"total": 100, "max": 100, "details": ["✅ Title length optimal (30-60 chars)", "✅ Description length optimal (120-160 chars)", "✅ Keywords meta tag present", "✅ Open Graph tags present", "✅ Twitter Card tags present", "✅ Structured data (JSON-LD) present", "✅ Canonical URL specified", "✅ Robots meta tags configured"]}, "technical": {"total": 50, "max": 50, "details": ["✅ robots.txt file present", "✅ Sitemap configuration present", "✅ Favicon present", "✅ Next.js optimization configured", "✅ Analytics component present"]}, "content": {"total": 0, "max": 30, "details": ["❌ No H1 heading found", "⚠️ Alt attributes may be missing", "⚠️ Limited semantic HTML (found: 1/6 tags)", "⚠️ Limited internal linking"]}, "recommendations": ["📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy"]}, {"name": "AI Platform", "path": "landing_ai/the-semantics-ai", "totalScore": 76, "grade": "B", "metadata": {"total": 85, "max": 100, "details": ["✅ Title length optimal (30-60 chars)", "⚠️ Description length: 9 chars (optimal: 120-160)", "✅ Keywords meta tag present", "✅ Open Graph tags present", "✅ Twitter Card tags present", "✅ Structured data (JSON-LD) present", "✅ Canonical URL specified", "✅ Robots meta tags configured"]}, "technical": {"total": 50, "max": 50, "details": ["✅ robots.txt file present", "✅ Sitemap configuration present", "✅ Favicon present", "✅ Next.js optimization configured", "✅ Analytics component present"]}, "content": {"total": 0, "max": 30, "details": ["❌ No H1 heading found", "⚠️ Alt attributes may be missing", "⚠️ Limited semantic HTML (found: 1/6 tags)", "⚠️ Limited internal linking"]}, "recommendations": ["📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy"]}, {"name": "Web Scraping", "path": "landing_scraping", "totalScore": 85, "grade": "A", "metadata": {"total": 100, "max": 100, "details": ["✅ Title length optimal (30-60 chars)", "✅ Description length optimal (120-160 chars)", "✅ Keywords meta tag present", "✅ Open Graph tags present", "✅ Twitter Card tags present", "✅ Structured data (JSON-LD) present", "✅ Canonical URL specified", "✅ Robots meta tags configured"]}, "technical": {"total": 50, "max": 50, "details": ["✅ robots.txt file present", "✅ Sitemap configuration present", "✅ Favicon present", "✅ Next.js optimization configured", "✅ Analytics component present"]}, "content": {"total": 0, "max": 30, "details": ["❌ No H1 heading found", "⚠️ Alt attributes may be missing", "⚠️ Limited semantic HTML (found: 1/6 tags)", "⚠️ Limited internal linking"]}, "recommendations": ["📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy"]}, {"name": "The Semantics", "path": "the-semantics", "totalScore": 85, "grade": "A", "metadata": {"total": 100, "max": 100, "details": ["✅ Title length optimal (30-60 chars)", "✅ Description length optimal (120-160 chars)", "✅ Keywords meta tag present", "✅ Open Graph tags present", "✅ Twitter Card tags present", "✅ Structured data (JSON-LD) present", "✅ Canonical URL specified", "✅ Robots meta tags configured"]}, "technical": {"total": 50, "max": 50, "details": ["✅ robots.txt file present", "✅ Sitemap configuration present", "✅ Favicon present", "✅ Next.js optimization configured", "✅ Analytics component present"]}, "content": {"total": 0, "max": 30, "details": ["❌ No H1 heading found", "⚠️ Alt attributes may be missing", "⚠️ Limited semantic HTML (found: 1/6 tags)", "⚠️ Limited internal linking"]}, "recommendations": ["📝 Enhance content structure: Add more semantic HTML and improve heading hierarchy"]}]