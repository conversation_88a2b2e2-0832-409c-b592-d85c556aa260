{"name": "seo-analyzer", "version": "1.0.0", "description": "Free SEO analyzer for The Semantics landing pages", "main": "seo-analyzer.js", "scripts": {"analyze": "node seo-analyzer.js", "install-deps": "npm install cheerio", "lighthouse": "npx lighthouse --view", "setup": "npm install && echo 'SEO Analyzer ready! Run: npm run analyze'"}, "keywords": ["seo", "analysis", "landing-pages", "free"], "author": "The Semantics", "license": "MIT", "dependencies": {"cheerio": "^1.0.0-rc.12"}, "devDependencies": {"lighthouse": "^11.4.0"}}