import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { WebVitals } from "@/components/analytics";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geist<PERSON><PERSON> = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "The Semantics - AI & Technology Solutions | LangChain",
  description: "Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development for businesses.",
  keywords: ["AI development", "artificial intelligence", "LangChain", "RAG", "machine learning", "data science", "semantic AI", "AI automation", "neural networks", "deep learning", "AI consulting"],
  authors: [{ name: "The Semantics AI Team" }],
  creator: "The Semantics",
  publisher: "The Semantics",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://thesemantics.com",
    title: "The Semantics - AI & Technology Solutions | LangChain",
    description: "Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development for businesses.",
    siteName: "The Semantics",
    images: [
      {
        url: "/ai-og-image.jpg",
        width: 1200,
        height: 630,
        alt: "The Semantics AI & Technology Solutions",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "The Semantics - AI & Technology Solutions | LangChain",
    description: "Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development for businesses.",
    creator: "@thesemantics",
    images: ["/ai-og-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://thesemantics.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Semantics",
    "url": "https://thesemantics.com",
    "logo": "https://thesemantics.com/logo.png",
    "description": "Leading AI and technology solutions provider specializing in LangChain RAG, machine learning, data science, and semantic AI development",
    "foundingDate": "2020",
    "sameAs": [
      "https://linkedin.com/company/thesemantics",
      "https://github.com/thesemantics",
      "https://twitter.com/thesemantics"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "service": [
      {
        "@type": "Service",
        "name": "AI Development",
        "description": "Custom artificial intelligence solutions using LangChain, RAG, and machine learning"
      },
      {
        "@type": "Service",
        "name": "Machine Learning",
        "description": "Advanced machine learning models and neural network development"
      },
      {
        "@type": "Service",
        "name": "Data Science",
        "description": "Comprehensive data science solutions and predictive analytics"
      },
      {
        "@type": "Service",
        "name": "Semantic AI",
        "description": "Semantic understanding and natural language processing solutions"
      },
      {
        "@type": "Service",
        "name": "AI Automation",
        "description": "Intelligent automation and workflow optimization using AI"
      }
    ],
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "AI & Technology Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "AI Development Services",
            "description": "Professional AI development and machine learning solutions"
          }
        }
      ]
    },
    "knowsAbout": [
      "Artificial Intelligence",
      "Machine Learning",
      "LangChain",
      "RAG (Retrieval-Augmented Generation)",
      "Data Science",
      "Neural Networks",
      "Deep Learning",
      "Natural Language Processing",
      "Computer Vision",
      "AI Automation",
      "Semantic AI",
      "Python",
      "TensorFlow",
      "PyTorch"
    ],
    "expertise": [
      "AI Strategy Consulting",
      "Custom AI Model Development",
      "LangChain Implementation",
      "RAG System Development",
      "Machine Learning Pipeline",
      "Data Science Analytics",
      "AI Integration Services"
    ]
  };

  return (
    <html lang="en">
      <head>
        {/* Explicit meta tags for better AI service SEO */}
        <meta name="description" content="Leading AI and technology solutions provider. Expert LangChain RAG, machine learning, data science, and semantic AI development for businesses." />
        <meta name="keywords" content="AI development, artificial intelligence, LangChain, RAG, machine learning, data science, semantic AI, AI automation, neural networks, deep learning, AI consulting" />
        <meta name="author" content="The Semantics AI Team" />
        <meta name="robots" content="index, follow" />

        {/* Preconnect to external domains for better AI service performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://openai.com" />
        <link rel="preconnect" href="https://api.openai.com" />
        <link rel="dns-prefetch" href="https://huggingface.co" />
        <link rel="dns-prefetch" href="https://github.com" />
        <link rel="dns-prefetch" href="https://www.linkedin.com" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebVitals />
        {children}
      </body>
    </html>
  );
}
