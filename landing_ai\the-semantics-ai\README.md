# The Semantics - Advanced AI Platform

A modern, responsive landing page for The Semantics AI platform built with Next.js, TypeScript, Tailwind CSS, and shadcn/ui components.

## 🚀 Features

- **Modern Design**: Clean, professional design with gradient accents and smooth animations
- **AI-Focused Content**: Showcases LangChain RAG, Data Science, and AI automation capabilities
- **Responsive**: Fully responsive design that works on all devices
- **Performance**: Built with Next.js 15 and optimized for speed
- **Accessibility**: Follows accessibility best practices
- **Dark Mode**: Built-in dark mode support via shadcn/ui

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Font**: Geist Sans & Geist Mono

## 📦 Installation

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Project Structure

```
src/
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── ui/
│   │   ├── button.tsx
│   │   ├── infinite-slider.tsx
│   │   └── progressive-blur.tsx
│   ├── about-section.tsx
│   ├── features-section.tsx
│   ├── footer.tsx
│   └── hero-section.tsx
└── lib/
    └── utils.ts
```

## 🎨 Components

### Hero Section
- Animated header with scroll-based backdrop blur
- Gradient branding for "The Semantics"
- Call-to-action buttons
- AI-themed background with icons
- Infinite slider with company logos

### Features Section
- Grid layout showcasing AI capabilities
- LangChain RAG, Data Science, Semantic Search
- AI Automation, Analytics, and Knowledge Graphs
- Performance metrics with animated counters
- Gradient backgrounds and hover effects

### About Section
- Company mission and values
- Team member profiles with photos
- Interactive timeline of company milestones
- Call-to-action for getting started

### Footer
- Comprehensive link organization
- Contact information
- Social media links
- Legal and resource links

## 🎯 Key Features Highlighted

1. **LangChain RAG**: Advanced Retrieval-Augmented Generation
2. **Data Science Platform**: ML pipelines and predictive modeling
3. **Semantic Search**: Context-aware intelligent search
4. **AI Automation**: Intelligent workflow automation
5. **Analytics & Insights**: Real-time AI-powered analytics
6. **Knowledge Graphs**: Graph-based AI relationships

## 🚀 Deployment

The project is ready for deployment on platforms like Vercel, Netlify, or any other hosting service that supports Next.js.

For Vercel deployment:
```bash
npm run build
```

## 📱 Responsive Design

The landing page is fully responsive with breakpoints for:
- Mobile (sm): 640px+
- Tablet (md): 768px+
- Desktop (lg): 1024px+
- Large Desktop (xl): 1280px+

## 🎨 Design System

- **Primary Colors**: Blue to Purple gradient
- **Typography**: Geist Sans for headings, system fonts for body
- **Spacing**: Consistent spacing using Tailwind's spacing scale
- **Animations**: Smooth transitions and scroll-based animations
- **Components**: Reusable UI components with consistent styling

## 🔧 Customization

To customize the content:

1. **Hero Section**: Edit `src/components/hero-section.tsx`
2. **Features**: Modify the features array in `src/components/features-section.tsx`
3. **About Content**: Update team and company info in `src/components/about-section.tsx`
4. **Styling**: Adjust colors and themes in `src/app/globals.css`

---

Built with ❤️ for The Semantics AI Platform
