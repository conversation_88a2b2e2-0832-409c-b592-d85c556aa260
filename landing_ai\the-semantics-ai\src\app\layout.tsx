import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { WebVitals } from "@/components/web-vitals";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistM<PERSON> = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "The Semantics - Advanced AI Platform | LangChain RAG",
  description: "Unlock AI's true potential with The Semantics advanced AI platform. Powered by LangChain RAG, cutting-edge data science, and semantic understanding. Transform your data into intelligent insights with enterprise-grade AI solutions.",
  keywords: ["advanced AI platform", "LangChain RAG", "semantic intelligence", "data science platform", "AI automation", "machine learning", "neural networks", "enterprise AI", "intelligent insights", "AI DNA", "semantic search"],
  authors: [{ name: "The Semantics AI Platform Team" }],
  creator: "The Semantics",
  publisher: "The Semantics",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://ai.thesemantics.com",
    title: "The Semantics - Advanced AI Platform | LangChain RAG",
    description: "Unlock AI's true potential with our advanced AI platform. Powered by LangChain RAG, cutting-edge data science, and semantic understanding for enterprise solutions.",
    siteName: "The Semantics AI Platform",
    images: [
      {
        url: "/ai-platform-og-image.jpg",
        width: 1200,
        height: 630,
        alt: "The Semantics Advanced AI Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "The Semantics - Advanced AI Platform | LangChain RAG",
    description: "Unlock AI's true potential with our advanced AI platform. Powered by LangChain RAG, data science, and semantic understanding. Transform your data into intelligent insights.",
    creator: "@thesemantics",
    images: ["/ai-platform-og-image.jpg"],
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://ai.thesemantics.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "The Semantics AI Platform",
    "url": "https://ai.thesemantics.com",
    "description": "Advanced AI platform powered by LangChain RAG, cutting-edge data science, and semantic understanding for enterprise-grade intelligent insights",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web-based",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "provider": {
      "@type": "Organization",
      "name": "The Semantics",
      "url": "https://thesemantics.com",
      "logo": "https://thesemantics.com/logo.png",
      "description": "Leading AI platform provider specializing in LangChain RAG, semantic intelligence, and enterprise AI solutions",
      "foundingDate": "2020",
      "sameAs": [
        "https://linkedin.com/company/thesemantics",
        "https://github.com/thesemantics",
        "https://twitter.com/thesemantics"
      ],
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "******-0123",
        "contactType": "customer service",
        "availableLanguage": "English"
      },
      "address": {
        "@type": "PostalAddress",
        "addressCountry": "US"
      }
    },
    "featureList": [
      "LangChain RAG Implementation",
      "Semantic Intelligence Processing",
      "Data Science Platform",
      "AI Automation Workflows",
      "Neural Network Integration",
      "Knowledge Graph Construction",
      "Intelligent Analytics Dashboard",
      "Enterprise AI Solutions"
    ],
    "softwareVersion": "2.0",
    "releaseNotes": "Advanced AI platform with enhanced LangChain RAG capabilities and semantic understanding",
    "screenshot": "https://thesemantics.com/ai-platform-screenshot.jpg",
    "video": "https://thesemantics.com/ai-platform-demo.mp4",
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "ratingCount": "127",
      "bestRating": "5",
      "worstRating": "1"
    },
    "review": [
      {
        "@type": "Review",
        "author": {
          "@type": "Person",
          "name": "Enterprise Client"
        },
        "reviewRating": {
          "@type": "Rating",
          "ratingValue": "5",
          "bestRating": "5"
        },
        "reviewBody": "The Semantics AI platform transformed our data processing capabilities with advanced LangChain RAG implementation."
      }
    ]
  };

  return (
    <html lang="en">
      <head>
        {/* Explicit meta tags for better AI platform SEO */}
        <meta name="description" content="Advanced AI platform with LangChain RAG, semantic intelligence, and enterprise data science solutions. Transform your business with cutting-edge AI technology." />
        <meta name="keywords" content="AI platform, LangChain, RAG, semantic intelligence, data science, machine learning, AI automation, enterprise AI, neural networks, deep learning" />
        <meta name="author" content="The Semantics AI Platform Team" />
        <meta name="robots" content="index, follow" />

        {/* Preconnect to external domains for better AI platform performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://openai.com" />
        <link rel="preconnect" href="https://api.openai.com" />
        <link rel="dns-prefetch" href="https://huggingface.co" />
        <link rel="dns-prefetch" href="https://github.com" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebVitals />
        {children}
      </body>
    </html>
  );
}
