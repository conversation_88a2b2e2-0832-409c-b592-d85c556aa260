import Head from 'next/head';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
  canonicalUrl?: string;
  structuredData?: object;
}

export function SEO({
  title = "The Semantics - Advanced AI Platform | LangChain RAG, Data Science & Semantic Intelligence",
  description = "Unlock AI's true potential with The Semantics advanced AI platform. Powered by LangChain RAG, cutting-edge data science, and semantic understanding. Transform your data into intelligent insights with enterprise-grade AI solutions.",
  keywords = ["advanced AI platform", "LangChain RAG", "semantic intelligence", "data science platform", "AI automation", "neural networks", "enterprise AI"],
  ogImage = "/ai-platform-og-image.jpg",
  canonicalUrl,
  structuredData
}: SEOProps) {
  const keywordsString = keywords.join(', ');

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywordsString} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      
      {/* AI platform specific meta tags */}
      <meta name="ai-platform" content="the-semantics-advanced" />
      <meta name="platform-features" content="langchain-rag,data-science,semantic-intelligence" />
      <meta name="enterprise-ready" content="true" />
      <meta name="ai-capabilities" content="langchain,rag,machine-learning,data-science,semantic-ai" />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:type" content="website" />
      <meta property="og:video" content="/ai-platform-demo.mp4" />
      
      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      <meta name="twitter:player" content="/ai-platform-demo.mp4" />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Structured Data */}
      {structuredData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      )}
    </Head>
  );
}
