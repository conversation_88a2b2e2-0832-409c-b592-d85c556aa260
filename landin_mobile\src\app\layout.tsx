import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { WebVitals } from "@/components/web-vitals";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "The Semantics - Mobile App Development | React Native",
  description: "Transform your business with cutting-edge mobile app development. Expert React Native, Flutter, and native iOS/Android development.",
  keywords: ["mobile app development", "React Native", "Flutter", "iOS development", "Android development", "cross-platform apps", "native mobile apps", "mobile UI/UX"],
  authors: [{ name: "The Semantics Mobile Team" }],
  creator: "The Semantics",
  publisher: "The Semantics",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://mobile.thesemantics.com",
    title: "The Semantics - Mobile App Development | React Native",
    description: "Transform your business with cutting-edge mobile app development. Expert React Native, Flutter, and native iOS/Android development.",
    siteName: "The Semantics",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Semantics - Mobile App Development | React Native",
    description: "Transform your business with cutting-edge mobile app development. Expert React Native, Flutter, and native iOS/Android development.",
    creator: "@thesemantics",
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://mobile.thesemantics.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Semantics",
    "url": "https://mobile.thesemantics.com",
    "logo": "https://thesemantics.com/logo.png",
    "description": "Expert mobile app development services specializing in React Native, Flutter, and native iOS/Android applications",
    "foundingDate": "2020",
    "sameAs": [
      "https://linkedin.com/company/thesemantics",
      "https://github.com/thesemantics",
      "https://twitter.com/thesemantics"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "service": [
      {
        "@type": "Service",
        "name": "React Native Development",
        "description": "Cross-platform mobile app development using React Native framework"
      },
      {
        "@type": "Service",
        "name": "Flutter Development",
        "description": "High-performance cross-platform mobile apps with Flutter"
      },
      {
        "@type": "Service",
        "name": "Native iOS Development",
        "description": "Native iOS app development using Swift and Objective-C"
      },
      {
        "@type": "Service",
        "name": "Native Android Development",
        "description": "Native Android app development using Kotlin and Java"
      }
    ],
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Mobile Development Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Mobile App Development",
            "description": "Custom mobile application development for iOS and Android"
          }
        }
      ]
    }
  };

  return (
    <html lang="en">
      <head>
        {/* Explicit meta tags for better mobile SEO */}
        <meta name="description" content="Transform your business with cutting-edge mobile app development. Expert React Native, Flutter, and native iOS/Android development." />
        <meta name="keywords" content="mobile app development, React Native, Flutter, iOS development, Android development, cross-platform apps, native mobile apps, mobile UI/UX" />
        <meta name="author" content="The Semantics Mobile Team" />
        <meta name="robots" content="index, follow" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />

        {/* Preconnect to external domains for better mobile performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://images.unsplash.com" />
        <link rel="dns-prefetch" href="https://www.linkedin.com" />
        <link rel="dns-prefetch" href="https://play.google.com" />
        <link rel="dns-prefetch" href="https://apps.apple.com" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebVitals />
        {children}
      </body>
    </html>
  );
}
