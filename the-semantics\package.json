{"name": "the-semantics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "build:prod": "next build && next export"}, "dependencies": {"@react-three/drei": "^10.5.1", "@react-three/fiber": "^9.2.0", "@splinetool/react-spline": "^4.1.0", "@splinetool/runtime": "^1.10.33", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "three": "^0.178.0", "web-vitals": "^5.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}