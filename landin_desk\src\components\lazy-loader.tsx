'use client';

import { Suspense, lazy } from 'react';

// Lazy load heavy components
export const LazyScene = lazy(() => import('./ui/hero-section').then(module => ({ default: module.Scene })));

// Loading fallback component
export function SceneLoader() {
  return (
    <div className="fixed inset-0 z-0 bg-gradient-to-br from-[#000] to-[#1A2428] flex items-center justify-center">
      <div className="text-white/50 text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white/30 mx-auto mb-4"></div>
        <p className="text-sm">Loading 3D Experience...</p>
      </div>
    </div>
  );
}

// Wrapper component with Suspense
export function OptimizedScene() {
  return (
    <Suspense fallback={<SceneLoader />}>
      <LazyScene />
    </Suspense>
  );
}
