'use client';

import { Suspense, lazy, useEffect } from 'react';
const Spline = lazy(() => import('@splinetool/react-spline'));

interface InteractiveRobotSplineProps {
  scene: string;
  className?: string;
}

export function InteractiveRobotSpline({ scene, className }: InteractiveRobotSplineProps) {
  useEffect(() => {
    // Function to hide only the Spline watermark, not the 3D content
    const hideWatermark = () => {
      // Only target specific watermark elements, not the canvas or 3D content
      const watermarkElements = document.querySelectorAll('a[href*="spline.design"]');
      watermarkElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        // Only hide if it's actually a watermark link
        if (htmlElement.getAttribute('href')?.includes('spline.design') &&
            (htmlElement.textContent?.includes('Built with Spline') ||
             htmlElement.textContent?.includes('Spline'))) {
          htmlElement.style.display = 'none';
          htmlElement.style.visibility = 'hidden';
          htmlElement.style.opacity = '0';
          htmlElement.style.pointerEvents = 'none';
        }
      });

      // Also hide any text elements that specifically contain watermark text
      const textElements = document.querySelectorAll('div, span, p, a');
      textElements.forEach((element) => {
        const htmlElement = element as HTMLElement;
        const text = htmlElement.textContent?.trim();
        if (text === 'Built with Spline' || text === 'Made with Spline') {
          htmlElement.style.display = 'none';
          htmlElement.style.visibility = 'hidden';
          htmlElement.style.opacity = '0';
        }
      });
    };

    // Run after a delay to let Spline load first
    const timer = setTimeout(() => {
      hideWatermark();
      const interval = setInterval(hideWatermark, 2000);

      // Clean up interval after component unmounts
      return () => clearInterval(interval);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="relative w-full h-full overflow-hidden">
      <Suspense
        fallback={
          <div className={`w-full h-full flex items-center justify-center bg-black text-white ${className}`}>
            <svg className="animate-spin h-5 w-5 text-white mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l2-2.647z"></path>
            </svg>
          </div>
        }
      >
        <div className="spline-container relative w-full h-full">
          <Spline
            scene={scene}
            className={className}
            style={{ width: '100%', height: '100%' }}
          />
        </div>
      </Suspense>
    </div>
  );
}
