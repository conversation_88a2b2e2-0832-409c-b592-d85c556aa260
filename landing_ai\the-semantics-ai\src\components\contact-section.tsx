'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/button'
import { Mail, Globe, Rocket, MessageSquare } from 'lucide-react'

export function ContactSection() {
  return (
    <section className="py-20 px-8" aria-label="Contact us">
      <div className="max-w-6xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">Transform</span> Your AI?
          </h2>
          <p className="text-muted-foreground text-lg max-w-3xl mx-auto">
            Get started with our advanced AI platform and unlock the power of semantic intelligence for your organization
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div>
              <h3 className="text-2xl font-bold mb-4">Start Your AI Journey</h3>
              <p className="text-muted-foreground leading-relaxed">
                Whether you need LangChain RAG implementation, semantic data processing, 
                or custom AI solutions, our team of experts is ready to help you unlock 
                the full potential of artificial intelligence.
              </p>
            </div>
            
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Mail className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">Email</p>
                  <a href="mailto:<EMAIL>" className="text-foreground hover:text-primary transition-colors font-medium">
                    <EMAIL>
                  </a>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                  <Globe className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">Professional Services</p>
                  <a href="https://www.fiverr.com/tahirccreative" target="_blank" rel="noopener noreferrer" className="text-foreground hover:text-primary transition-colors font-medium">
                    View Our Fiverr Profile
                  </a>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <p className="text-muted-foreground text-sm">LinkedIn</p>
                  <a href="https://www.linkedin.com/company/thesemanticsco/" target="_blank" rel="noopener noreferrer" className="text-foreground hover:text-primary transition-colors font-medium">
                    Connect with us
                  </a>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="bg-card/50 backdrop-blur-sm border border-border rounded-2xl p-8"
          >
            <h3 className="text-xl font-bold mb-6">Get Started Today</h3>
            <div className="space-y-6">
              <div className="space-y-4">
                <Button asChild className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
                  <a href="mailto:<EMAIL>">
                    <Mail className="w-4 h-4 mr-2" />
                    Send Email
                  </a>
                </Button>
                
                <Button asChild variant="outline" className="w-full">
                  <a href="https://www.fiverr.com/tahirccreative" target="_blank" rel="noopener noreferrer">
                    <Rocket className="w-4 h-4 mr-2" />
                    Hire on Fiverr
                  </a>
                </Button>

                <Button asChild variant="outline" className="w-full">
                  <a href="https://www.linkedin.com/company/thesemanticsco/" target="_blank" rel="noopener noreferrer">
                    <Globe className="w-4 h-4 mr-2" />
                    LinkedIn
                  </a>
                </Button>
              </div>
              
              <div className="text-center pt-4 border-t border-border">
                <p className="text-muted-foreground text-sm">
                  <strong>Response time:</strong> Usually within 2-4 hours
                </p>
                <p className="text-muted-foreground text-xs mt-2">
                  Available for custom AI solutions, LangChain implementations, and enterprise consulting
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
