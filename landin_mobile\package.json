{"name": "the-semantics", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "build:prod": "next build && next export"}, "dependencies": {"@splinetool/react-spline": "^4.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.3", "react": "19.1.0", "react-dom": "19.1.0", "react-intersection-observer": "^9.16.0", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.1", "web-vitals": "^5.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}