{"name": "the-semantics-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "cross-env ANALYZE=true next build", "build:prod": "next build && next export"}, "dependencies": {"@radix-ui/react-slot": "^1.2.3", "@types/three": "^0.178.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "critters": "^0.0.23", "framer-motion": "^12.23.6", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "motion": "^12.23.6", "next": "15.4.2", "react": "19.1.0", "react-dom": "19.1.0", "react-intersection-observer": "^9.16.0", "react-use-measure": "^2.1.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "web-vitals": "^5.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.4.2", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.5", "typescript": "^5"}}