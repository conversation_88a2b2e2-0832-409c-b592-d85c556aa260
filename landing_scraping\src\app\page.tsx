import WebScrapingDemo from "@/components/web-scraping-demo";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "The Semantics - Professional Web Scraping Services | Python Data Extraction",
  description: "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical, and scalable data collection solutions for businesses worldwide.",
  openGraph: {
    title: "The Semantics - Professional Web Scraping Services",
    description: "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical, and scalable data collection solutions.",
    type: "website",
  },
};

export default function Home() {
  return (
    <main className="min-h-screen" role="main" aria-label="Main content">
      <WebScrapingDemo />
    </main>
  );
}
