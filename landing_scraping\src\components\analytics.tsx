'use client';

import { useEffect } from 'react';

// Google Analytics component
export function GoogleAnalytics({ gaId }: { gaId: string }) {
  useEffect(() => {
    // Load Google Analytics
    const script1 = document.createElement('script');
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script1.async = true;
    document.head.appendChild(script1);

    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        // Enhanced web scraping service tracking
        send_page_view: true,
        custom_map: {
          'custom_parameter_1': 'scraping_service_interest',
          'custom_parameter_2': 'data_extraction_type'
        }
      });
    `;
    document.head.appendChild(script2);

    return () => {
      document.head.removeChild(script1);
      document.head.removeChild(script2);
    };
  }, [gaId]);

  return null;
}

// Core Web Vitals monitoring for web scraping services
export function WebVitals() {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        // Log Core Web Vitals with scraping service context
        const logVital = (metric: any) => {
          console.log({
            ...metric,
            service: 'web-scraping',
            timestamp: Date.now()
          });
          
          // Send to analytics if available
          if ((window as any).gtag) {
            (window as any).gtag('event', metric.name, {
              event_category: 'Web Vitals - Scraping',
              event_label: 'web-scraping-service',
              value: Math.round(metric.value),
              non_interaction: true,
            });
          }
        };

        getCLS(logVital);
        getFID(logVital);
        getFCP(logVital);
        getLCP(logVital);
        getTTFB(logVital);
      });
    }
  }, []);

  return null;
}

// Web scraping service-specific event tracking
export function trackScrapingEvent(action: string, category: string = 'Web Scraping', label?: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      custom_parameter_1: 'scraping_interaction'
    });
  }
}

// Track scraping service interest
export function trackScrapingInterest(serviceType: 'beautifulsoup' | 'scrapy' | 'data-mining' | 'api-development') {
  trackScrapingEvent('service_interest', 'Scraping Services', serviceType);
}

// Track data extraction inquiries
export function trackDataExtractionInquiry(dataType: string) {
  trackScrapingEvent('data_extraction_inquiry', 'Data Extraction', dataType);
}

// Track ethical scraping practices engagement
export function trackEthicalScrapingEngagement() {
  trackScrapingEvent('ethical_practices_view', 'Ethical Scraping', 'compliance');
}

// Simple page view tracking
export function trackPageView(url: string) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_ID, {
      page_path: url,
    });
  }
}
