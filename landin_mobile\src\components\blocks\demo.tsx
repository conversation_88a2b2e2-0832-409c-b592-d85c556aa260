'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Smartphone, Zap, Code } from 'lucide-react';
import { InteractiveRobotSpline } from '@/components/ui/interactive-3d-robot'

export function HeroSection() {

  const ROBOT_SCENE_URL = "https://prod.spline.design/PyzDhpQ9E5f1E3MT/scene.splinecode";

  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.8, ease: "easeOut" }
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  return (
    <section className="relative w-screen h-screen overflow-hidden bg-black" aria-label="Mobile development hero section">
      <InteractiveRobotSpline
        scene={ROBOT_SCENE_URL}
        className="absolute inset-0 z-0"
      />

      {/* Gradient overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/20 via-transparent to-black/40 z-5" />

      <motion.div
        className="absolute inset-0 z-10 flex flex-col justify-center items-center px-4 md:px-8"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        <header className="text-center text-white max-w-4xl mx-auto">

          {/* Main Heading */}
          <motion.div
            variants={fadeInUp}
            className="mb-6"
          >
            <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-4 bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent">
              Mobile App Development
            </h1>
            <h2 className="text-2xl md:text-4xl lg:text-5xl font-semibold text-blue-400">
              Redefined by The Semantics
            </h2>
          </motion.div>

          {/* Subtitle */}
          <motion.p
            variants={fadeInUp}
            className="text-lg md:text-xl lg:text-2xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed"
          >
            Expert React Native, Flutter, and native iOS/Android development. We craft exceptional mobile applications
            that deliver outstanding user experiences and drive meaningful business growth.
          </motion.p>

          {/* Feature Pills */}
          <motion.div
            variants={fadeInUp}
            className="flex flex-wrap justify-center gap-4 mb-10"
          >
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <Smartphone className="w-4 h-4 text-blue-400" />
              <span className="text-sm font-medium">Native iOS & Android</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <Code className="w-4 h-4 text-purple-400" />
              <span className="text-sm font-medium">Cross-Platform</span>
            </div>
            <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20">
              <Zap className="w-4 h-4 text-yellow-400" />
              <span className="text-sm font-medium">Lightning Fast</span>
            </div>
          </motion.div>
        </header>
      </motion.div>
    </section>
  );
}
