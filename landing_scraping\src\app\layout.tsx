import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import { WebVitals } from "@/components/web-vitals";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
  display: "swap",
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "The Semantics - Web Scraping Services | Python & Scrapy",
  description: "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical data collection solutions for businesses.",
  keywords: ["web scraping", "data extraction", "Python scraping", "BeautifulSoup", "Scrapy", "data mining", "web crawling", "API development", "data collection"],
  authors: [{ name: "The Semantics Data Team" }],
  creator: "The Semantics",
  publisher: "The Semantics",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://scraping.thesemantics.com",
    title: "The Semantics - Web Scraping Services | Python & Scrapy",
    description: "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical data collection solutions for businesses.",
    siteName: "The Semantics",
  },
  twitter: {
    card: "summary_large_image",
    title: "The Semantics - Web Scraping Services | Python & Scrapy",
    description: "Expert web scraping and data extraction services using Python, BeautifulSoup, and Scrapy. Legal, ethical data collection solutions for businesses.",
    creator: "@thesemantics",
  },
  verification: {
    google: "your-google-verification-code",
  },
  alternates: {
    canonical: "https://scraping.thesemantics.com",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "The Semantics",
    "url": "https://scraping.thesemantics.com",
    "logo": "https://thesemantics.com/logo.png",
    "description": "Professional web scraping and data extraction services using Python, BeautifulSoup, Scrapy, and advanced data mining techniques",
    "foundingDate": "2020",
    "sameAs": [
      "https://linkedin.com/company/thesemantics",
      "https://github.com/thesemantics",
      "https://twitter.com/thesemantics"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-0123",
      "contactType": "customer service",
      "availableLanguage": "English"
    },
    "address": {
      "@type": "PostalAddress",
      "addressCountry": "US"
    },
    "service": [
      {
        "@type": "Service",
        "name": "Web Scraping Services",
        "description": "Professional web scraping and data extraction using Python, BeautifulSoup, and Scrapy"
      },
      {
        "@type": "Service",
        "name": "Data Mining",
        "description": "Advanced data mining and analysis from web sources with ethical practices"
      },
      {
        "@type": "Service",
        "name": "API Development",
        "description": "Custom API development for data access and integration"
      },
      {
        "@type": "Service",
        "name": "Data Processing",
        "description": "Data cleaning, transformation, and analysis using Pandas and NumPy"
      }
    ],
    "areaServed": "Worldwide",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Web Scraping Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Professional Web Scraping",
            "description": "Legal and ethical web scraping services for business data collection"
          }
        }
      ]
    },
    "knowsAbout": [
      "Python Programming",
      "BeautifulSoup",
      "Scrapy Framework",
      "Data Extraction",
      "Web Crawling",
      "Data Mining",
      "API Development",
      "Data Processing"
    ]
  };

  return (
    <html lang="en">
      <head>
        {/* Explicit meta tags for better scraping service SEO */}
        <meta name="description" content="Professional web scraping services with Python, BeautifulSoup, and Scrapy. Legal and ethical data extraction for business intelligence and automation." />
        <meta name="keywords" content="web scraping, data extraction, Python scraping, BeautifulSoup, Scrapy, data mining, web crawling, business intelligence, data automation" />
        <meta name="author" content="The Semantics Scraping Team" />
        <meta name="robots" content="index, follow" />

        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://images.unsplash.com" />
        <link rel="dns-prefetch" href="https://www.linkedin.com" />
        <link rel="dns-prefetch" href="https://github.com" />
        <link rel="dns-prefetch" href="https://stackoverflow.com" />

        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <WebVitals />
        {children}
      </body>
    </html>
  );
}
