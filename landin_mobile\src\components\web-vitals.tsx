'use client';

import { useReportWebVitals } from 'next/web-vitals';

export function WebVitals() {
  useReportWebVitals((metric) => {
    // Log metrics for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Mobile Web Vitals:', metric);
    }
    
    // In production, you could send these to an analytics service
    // Example: analytics.track('Mobile Web Vitals', metric);
  });

  return null;
}
