'use client';

import { useReportWebVitals } from 'next/web-vitals';

export function WebVitals() {
  useReportWebVitals((metric) => {
    // Log metrics for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('AI Platform Web Vitals:', metric);
    }
    
    // In production, you could send these to an analytics service
    // Example: analytics.track('AI Platform Web Vitals', metric);
  });

  return null;
}
